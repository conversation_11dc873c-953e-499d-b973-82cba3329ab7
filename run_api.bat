@echo off
REM run_api.bat: Launch the Llasa TTS API server on Windows

echo Starting Llasa TTS Voice Cloning API Server...
echo ==============================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "src\main.py" (
    echo Error: Please run this script from the project root directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Install API dependencies if needed
echo Checking API dependencies...
pip install -q fastapi uvicorn python-multipart soundfile requests

echo.
echo Starting API server on http://localhost:8000
echo Available endpoints:
echo   - GET  /health              - Health check
echo   - POST /api/voice-clone     - Voice cloning with reference audio
echo   - POST /api/text-to-speech  - Text-only speech synthesis
echo.
echo Press Ctrl+C to stop the server
echo.

REM Launch the API server
python -m src.main --mode api --api-port 8000
pause
