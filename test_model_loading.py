#!/usr/bin/env python3
"""
Test script to verify that models are loaded only once at startup.
This script monitors model loading behavior and timing.
"""

import time
import requests
import sys


def test_model_loading_efficiency():
    """Test that models are loaded once and reused for multiple requests."""
    
    print("Testing Model Loading Efficiency")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Check API health first
    print("1. Checking API health...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ API is healthy")
            print(f"   Models initialized: {data.get('models_initialized', 'Unknown')}")
            print(f"   CUDA available: {data.get('cuda_available', 'Unknown')}")
            if not data.get('models_initialized', False):
                print("   ⚠️  Models not initialized - this may indicate a problem")
        else:
            print(f"   ✗ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ Cannot connect to API: {e}")
        print("   Make sure the API server is running with: python -m src.main --mode api")
        return False
    
    # Test multiple requests to ensure models aren't reloaded
    print("\n2. Testing multiple API requests...")
    test_text = "This is a test to verify efficient model loading."
    
    request_times = []
    
    for i in range(3):
        print(f"\n   Request {i+1}/3:")
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{base_url}/api/text-to-speech",
                data={"text": test_text}
            )
            
            end_time = time.time()
            request_time = end_time - start_time
            request_times.append(request_time)
            
            if response.status_code == 200:
                print(f"   ✓ Success in {request_time:.2f}s")
                
                # Save audio file for verification
                filename = f"test_request_{i+1}.wav"
                with open(filename, "wb") as f:
                    f.write(response.content)
                print(f"   Audio saved as: {filename}")
            else:
                print(f"   ✗ Failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"   ✗ Error: {e}")
            return False
        
        # Small delay between requests
        time.sleep(0.5)
    
    # Analyze timing results
    print("\n3. Analyzing request timing:")
    print(f"   Request 1: {request_times[0]:.2f}s")
    print(f"   Request 2: {request_times[1]:.2f}s") 
    print(f"   Request 3: {request_times[2]:.2f}s")
    
    avg_time = sum(request_times) / len(request_times)
    print(f"   Average: {avg_time:.2f}s")
    
    # Check if subsequent requests are faster (indicating model reuse)
    if len(request_times) >= 2:
        first_request = request_times[0]
        subsequent_avg = sum(request_times[1:]) / len(request_times[1:])
        
        print(f"\n4. Model loading efficiency analysis:")
        print(f"   First request: {first_request:.2f}s")
        print(f"   Subsequent requests average: {subsequent_avg:.2f}s")
        
        if subsequent_avg < first_request * 0.8:  # Subsequent requests should be at least 20% faster
            print("   ✓ EFFICIENT: Subsequent requests are faster (models are reused)")
            efficiency_good = True
        elif abs(subsequent_avg - first_request) < 1.0:  # Within 1 second is acceptable
            print("   ✓ ACCEPTABLE: Request times are consistent (models likely reused)")
            efficiency_good = True
        else:
            print("   ⚠️  INEFFICIENT: Request times are inconsistent (models may be reloading)")
            efficiency_good = False
            
        return efficiency_good
    
    return True


def test_memory_usage():
    """Test GPU memory usage to ensure models aren't duplicated."""
    
    print("\n" + "=" * 50)
    print("Testing GPU Memory Usage")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # Get initial memory usage
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            data = response.json()
            initial_memory = data.get('gpu_memory_gb', 'N/A')
            print(f"Initial GPU memory usage: {initial_memory} GB")
            
            # Make a few requests and check memory again
            for i in range(2):
                requests.post(
                    f"{base_url}/api/text-to-speech",
                    data={"text": f"Memory test request {i+1}"}
                )
            
            # Check memory after requests
            response = requests.get(f"{base_url}/health")
            if response.status_code == 200:
                data = response.json()
                final_memory = data.get('gpu_memory_gb', 'N/A')
                print(f"Final GPU memory usage: {final_memory} GB")
                
                if initial_memory != 'N/A' and final_memory != 'N/A':
                    try:
                        initial_val = float(initial_memory)
                        final_val = float(final_memory)
                        memory_increase = final_val - initial_val
                        
                        if memory_increase < 0.5:  # Less than 500MB increase is good
                            print(f"✓ GOOD: Memory increase is minimal ({memory_increase:.2f} GB)")
                            return True
                        else:
                            print(f"⚠️  WARNING: Significant memory increase ({memory_increase:.2f} GB)")
                            return False
                    except ValueError:
                        print("Could not parse memory values for comparison")
                        return True
                else:
                    print("Memory values not available for comparison")
                    return True
        
    except Exception as e:
        print(f"Error testing memory usage: {e}")
        return False
    
    return True


def main():
    """Main function."""
    print("Model Loading Efficiency Test")
    print("This test verifies that models are loaded once at startup")
    print("and reused efficiently for subsequent API requests.")
    print()
    
    # Test model loading efficiency
    efficiency_good = test_model_loading_efficiency()
    
    # Test memory usage
    memory_good = test_memory_usage()
    
    # Final results
    print("\n" + "=" * 50)
    print("Final Results:")
    print("=" * 50)
    
    if efficiency_good:
        print("✅ Model Loading Efficiency: PASS")
    else:
        print("❌ Model Loading Efficiency: FAIL")
    
    if memory_good:
        print("✅ Memory Usage: PASS")
    else:
        print("❌ Memory Usage: FAIL")
    
    if efficiency_good and memory_good:
        print("\n🎉 All tests passed! Models are loaded efficiently.")
        print("\nKey benefits:")
        print("- Models load once at startup")
        print("- Subsequent requests are fast")
        print("- Memory usage is stable")
        print("- No model reloading overhead")
    else:
        print("\n⚠️  Some efficiency issues detected.")
        print("Check the output above for details.")
    
    return 0 if (efficiency_good and memory_good) else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
