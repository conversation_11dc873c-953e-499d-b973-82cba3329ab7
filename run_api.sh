#!/bin/bash
# run_api.sh: Launch the Llasa TTS API server

echo "Starting Llasa TTS Voice Cloning API Server..."
echo "=============================================="

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "Error: Python is not installed or not in PATH"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "src/main.py" ]; then
    echo "Error: Please run this script from the project root directory"
    echo "Current directory: $(pwd)"
    exit 1
fi

# Install API dependencies if needed
echo "Checking API dependencies..."
pip install -q fastapi uvicorn python-multipart soundfile requests

echo ""
echo "Starting API server on http://localhost:8000"
echo "Available endpoints:"
echo "  - GET  /health              - Health check"
echo "  - POST /api/voice-clone     - Voice cloning with reference audio"
echo "  - POST /api/text-to-speech  - Text-only speech synthesis"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Launch the API server
python -m src.main --mode api --api-port 8000
