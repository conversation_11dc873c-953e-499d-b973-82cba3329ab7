#!/usr/bin/env python3
"""
Test script for the Llasa TTS Voice Cloning API.
Tests both voice cloning and text-to-speech endpoints.
"""

import requests
import os
import sys


def test_health_check(base_url="http://localhost:8000"):
    """Test the health check endpoint."""
    print("Testing health check endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Health check passed: {data}")
            return True
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Health check error: {e}")
        return False


def test_text_to_speech(base_url="http://localhost:8000", text="Hello, this is a test of the text to speech API."):
    """Test the text-to-speech endpoint."""
    print("Testing text-to-speech endpoint...")
    try:
        data = {
            "text": text
        }
        
        response = requests.post(f"{base_url}/api/text-to-speech", data=data)
        
        if response.status_code == 200:
            # Save the audio file
            output_path = "test_tts_output.wav"
            with open(output_path, "wb") as f:
                f.write(response.content)
            
            print(f"✓ Text-to-speech successful! Audio saved to {output_path}")
            print(f"  Sample rate: {response.headers.get('X-Sample-Rate', 'Unknown')}")
            print(f"  Seed used: {response.headers.get('X-Seed-Used', 'Unknown')}")
            print(f"  Text length: {response.headers.get('X-Text-Length', 'Unknown')}")
            return True
        else:
            print(f"✗ Text-to-speech failed: {response.status_code}")
            print(f"  Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Text-to-speech error: {e}")
        return False


def test_voice_clone(base_url="http://localhost:8000", audio_file_path=None, text="This is a test of voice cloning with reference audio."):
    """Test the voice cloning endpoint."""
    print("Testing voice cloning endpoint...")

    if not audio_file_path:
        print("✗ No reference audio file provided for voice cloning test")
        return False

    if not os.path.exists(audio_file_path):
        print(f"✗ Reference audio file not found: {audio_file_path}")
        return False

    try:
        # Prepare form data (simplified - only text needed)
        data = {
            "text": text
        }
        
        # Prepare file upload
        with open(audio_file_path, "rb") as audio_file:
            files = {"reference_audio": (os.path.basename(audio_file_path), audio_file, "audio/wav")}
            
            response = requests.post(f"{base_url}/api/voice-clone", data=data, files=files)
        
        if response.status_code == 200:
            # Save the audio file
            output_path = "test_voice_clone_output.wav"
            with open(output_path, "wb") as f:
                f.write(response.content)
            
            print(f"✓ Voice cloning successful! Audio saved to {output_path}")
            print(f"  Sample rate: {response.headers.get('X-Sample-Rate', 'Unknown')}")
            print(f"  Seed used: {response.headers.get('X-Seed-Used', 'Unknown')}")
            print(f"  Text length: {response.headers.get('X-Text-Length', 'Unknown')}")
            return True
        else:
            print(f"✗ Voice cloning failed: {response.status_code}")
            print(f"  Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Voice cloning error: {e}")
        return False


def test_api_info(base_url="http://localhost:8000"):
    """Test the root API info endpoint."""
    print("Testing API info endpoint...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ API info retrieved: {data['message']}")
            print(f"  Version: {data['version']}")
            print(f"  Available endpoints: {list(data['endpoints'].keys())}")
            return True
        else:
            print(f"✗ API info failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ API info error: {e}")
        return False


def create_test_audio():
    """Create a simple test audio file for voice cloning tests."""
    try:
        import numpy as np
        import soundfile as sf
        
        # Generate a simple sine wave test audio (5 seconds)
        sample_rate = 16000
        duration = 5.0
        frequency = 440  # A4 note
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio = 0.3 * np.sin(2 * np.pi * frequency * t)
        
        # Add some variation to make it more speech-like
        audio += 0.1 * np.sin(2 * np.pi * frequency * 2 * t)
        audio += 0.05 * np.sin(2 * np.pi * frequency * 3 * t)
        
        # Apply envelope to make it more natural
        envelope = np.exp(-t / 2)
        audio *= envelope
        
        output_path = "test_reference_audio.wav"
        sf.write(output_path, audio, sample_rate)
        print(f"✓ Created test reference audio: {output_path}")
        return output_path
        
    except ImportError:
        print("✗ Cannot create test audio: soundfile not available")
        return None
    except Exception as e:
        print(f"✗ Error creating test audio: {e}")
        return None


def main():
    """Run all API tests."""
    print("=" * 60)
    print("Llasa TTS API Test Suite")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Check if server is running
    print(f"Testing API server at {base_url}")
    print("-" * 40)
    
    # Test basic endpoints
    tests_passed = 0
    total_tests = 0
    
    # Test 1: API Info
    total_tests += 1
    if test_api_info(base_url):
        tests_passed += 1
    print()
    
    # Test 2: Health Check
    total_tests += 1
    if test_health_check(base_url):
        tests_passed += 1
    print()
    
    # Test 3: Text-to-Speech
    total_tests += 1
    if test_text_to_speech(base_url):
        tests_passed += 1
    print()
    
    # Test 4: Voice Cloning (if reference audio is available)
    reference_audio = None
    if len(sys.argv) > 1:
        reference_audio = sys.argv[1]
    else:
        # Try to create a test audio file
        reference_audio = create_test_audio()
    
    if reference_audio:
        total_tests += 1
        if test_voice_clone(base_url, reference_audio):
            tests_passed += 1
        print()
    else:
        print("Skipping voice cloning test (no reference audio available)")
        print("Usage: python test_api.py [reference_audio.wav]")
        print()
    
    # Summary
    print("=" * 60)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
