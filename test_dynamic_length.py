#!/usr/bin/env python3
"""
Test script to demonstrate the improved dynamic length handling.
This script tests various text lengths to ensure the audio output matches the input text exactly.
"""

import requests
import os
import sys
import time


def test_dynamic_length(base_url="http://localhost:8000"):
    """Test the dynamic length optimization with various text lengths."""
    
    print("Testing Dynamic Length Optimization")
    print("=" * 50)
    
    # Test cases with different text lengths
    test_cases = [
        {
            "name": "Short text",
            "text": "Hello world!",
            "expected_duration": "1-2 seconds"
        },
        {
            "name": "Medium text", 
            "text": "This is a medium length sentence that should take about 5 to 8 seconds to say naturally.",
            "expected_duration": "5-8 seconds"
        },
        {
            "name": "Long text",
            "text": "This is a much longer piece of text that contains multiple sentences and should take significantly longer to synthesize. The dynamic length optimization should calculate the appropriate generation length based on the word count and content complexity. This ensures that the audio output matches the input text exactly without cutting off or unnecessary stretching.",
            "expected_duration": "15-20 seconds"
        },
        {
            "name": "Very short",
            "text": "Hi!",
            "expected_duration": "0.5-1 second"
        },
        {
            "name": "Complex text",
            "text": "The quick brown fox jumps over the lazy dog. This pangram contains every letter of the alphabet and is commonly used for testing text-to-speech systems. The dynamic length optimization should handle this complex sentence structure appropriately.",
            "expected_duration": "12-15 seconds"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Text: {test_case['text']}")
        print(f"Expected duration: {test_case['expected_duration']}")
        print(f"Word count: {len(test_case['text'].split())} words")
        
        # Test with text-to-speech endpoint
        success = test_text_synthesis(base_url, test_case['text'], f"test_{i}_{test_case['name'].replace(' ', '_')}.wav")
        
        results.append({
            "test": test_case['name'],
            "success": success,
            "word_count": len(test_case['text'].split()),
            "expected": test_case['expected_duration']
        })
        
        # Small delay between tests
        time.sleep(1)
    
    # Print summary
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print("=" * 50)
    
    for result in results:
        status = "✓ PASS" if result['success'] else "✗ FAIL"
        print(f"{status} {result['test']} ({result['word_count']} words) - Expected: {result['expected']}")
    
    successful_tests = sum(1 for r in results if r['success'])
    print(f"\nOverall: {successful_tests}/{len(results)} tests passed")
    
    return successful_tests == len(results)


def test_text_synthesis(base_url, text, output_filename):
    """Test text-to-speech synthesis with dynamic length."""
    
    try:
        url = f"{base_url}/api/text-to-speech"
        data = {"text": text}
        
        print("  Sending request...")
        start_time = time.time()
        response = requests.post(url, data=data)
        end_time = time.time()
        
        if response.status_code == 200:
            # Save the audio file
            with open(output_filename, "wb") as f:
                f.write(response.content)
            
            # Get file size for rough duration estimate
            file_size = os.path.getsize(output_filename)
            estimated_duration = file_size / (16000 * 2)  # 16kHz, 16-bit = 2 bytes per sample
            
            print(f"  ✓ Success! Generated: {output_filename}")
            print(f"  Processing time: {end_time - start_time:.1f}s")
            print(f"  File size: {file_size:,} bytes")
            print(f"  Estimated audio duration: {estimated_duration:.1f}s")
            
            # Check response headers for additional info
            if 'X-Sample-Rate' in response.headers:
                print(f"  Sample rate: {response.headers['X-Sample-Rate']} Hz")
            if 'X-Seed-Used' in response.headers:
                print(f"  Seed used: {response.headers['X-Seed-Used']}")
            if 'X-Text-Length' in response.headers:
                print(f"  Text length processed: {response.headers['X-Text-Length']} chars")
            
            return True
        else:
            print(f"  ✗ Failed: {response.status_code}")
            print(f"  Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ✗ Error: {e}")
        return False


def check_api_health(base_url="http://localhost:8000"):
    """Check if the API is running and healthy."""
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ API is healthy")
            print(f"  Models initialized: {data.get('models_initialized', 'Unknown')}")
            print(f"  CUDA available: {data.get('cuda_available', 'Unknown')}")
            return True
        else:
            print(f"✗ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Cannot connect to API: {e}")
        print("  Make sure the API server is running:")
        print("  python -m src.main --mode api")
        return False


def main():
    """Main function."""
    print("Dynamic Length Optimization Test")
    print("This test verifies that the TTS system generates audio")
    print("that matches the input text length without cutting or stretching.")
    print()
    
    base_url = "http://localhost:8000"
    
    # Check if API is running
    if not check_api_health(base_url):
        return 1
    
    # Run the tests
    success = test_dynamic_length(base_url)
    
    if success:
        print("\n🎉 All tests passed! Dynamic length optimization is working correctly.")
        print("\nGenerated audio files:")
        for file in os.listdir("."):
            if file.startswith("test_") and file.endswith(".wav"):
                print(f"  - {file}")
        print("\nListen to these files to verify the audio matches the expected text length.")
    else:
        print("\n❌ Some tests failed. Check the output above for details.")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
