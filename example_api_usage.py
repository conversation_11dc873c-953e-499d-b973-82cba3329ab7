#!/usr/bin/env python3
"""
Simple example demonstrating the Llasa TTS Voice Cloning API usage.
This script shows how to use both voice cloning and text-to-speech endpoints.
"""

import requests
import os
import sys


def voice_clone_example(reference_audio_path, text_to_synthesize):
    """
    Example of voice cloning using reference audio.
    
    Args:
        reference_audio_path: Path to reference audio file
        text_to_synthesize: Text to synthesize in the cloned voice
    """
    print("=== Voice Cloning Example ===")
    print(f"Reference audio: {reference_audio_path}")
    print(f"Text to synthesize: {text_to_synthesize}")
    
    if not os.path.exists(reference_audio_path):
        print(f"Error: Reference audio file not found: {reference_audio_path}")
        return False
    
    url = "http://localhost:8000/api/voice-clone"
    
    # Prepare the request data (only text needed - all other parameters are auto-optimized)
    data = {
        "text": text_to_synthesize
    }
    
    try:
        # Send the request with reference audio file
        with open(reference_audio_path, "rb") as audio_file:
            files = {"reference_audio": audio_file}
            print("Sending request to API...")
            response = requests.post(url, data=data, files=files)
        
        if response.status_code == 200:
            # Save the synthesized audio
            output_path = "voice_clone_output.wav"
            with open(output_path, "wb") as f:
                f.write(response.content)
            
            print(f"✓ Voice cloning successful!")
            print(f"  Output saved to: {output_path}")
            print(f"  Sample rate: {response.headers.get('X-Sample-Rate', 'Unknown')}")
            print(f"  Seed used: {response.headers.get('X-Seed-Used', 'Unknown')}")
            print(f"  Text length: {response.headers.get('X-Text-Length', 'Unknown')}")
            return True
        else:
            print(f"✗ Voice cloning failed: {response.status_code}")
            print(f"  Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Error during voice cloning: {e}")
        return False


def text_to_speech_example(text_to_synthesize):
    """
    Example of text-to-speech without reference audio.
    
    Args:
        text_to_synthesize: Text to convert to speech
    """
    print("\n=== Text-to-Speech Example ===")
    print(f"Text to synthesize: {text_to_synthesize}")
    
    url = "http://localhost:8000/api/text-to-speech"
    
    # Prepare the request data (only text needed - all other parameters are auto-optimized)
    data = {
        "text": text_to_synthesize
    }
    
    try:
        print("Sending request to API...")
        response = requests.post(url, data=data)
        
        if response.status_code == 200:
            # Save the synthesized audio
            output_path = "text_to_speech_output.wav"
            with open(output_path, "wb") as f:
                f.write(response.content)
            
            print(f"✓ Text-to-speech successful!")
            print(f"  Output saved to: {output_path}")
            print(f"  Sample rate: {response.headers.get('X-Sample-Rate', 'Unknown')}")
            print(f"  Seed used: {response.headers.get('X-Seed-Used', 'Unknown')}")
            print(f"  Text length: {response.headers.get('X-Text-Length', 'Unknown')}")
            return True
        else:
            print(f"✗ Text-to-speech failed: {response.status_code}")
            print(f"  Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Error during text-to-speech: {e}")
        return False


def check_api_status():
    """Check if the API server is running and ready."""
    print("=== Checking API Status ===")
    
    try:
        # Check health endpoint
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ API server is healthy")
            print(f"  Models initialized: {data.get('models_initialized', 'Unknown')}")
            print(f"  CUDA available: {data.get('cuda_available', 'Unknown')}")
            return True
        else:
            print(f"✗ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Cannot connect to API server: {e}")
        print("  Make sure the API server is running on http://localhost:8000")
        print("  Start it with: python -m src.main --mode api")
        return False


def main():
    """Main function demonstrating API usage."""
    print("Llasa TTS Voice Cloning API - Usage Example")
    print("=" * 50)
    
    # Check if API server is running
    if not check_api_status():
        return 1
    
    # Example texts
    voice_clone_text = "Hello! This is an example of voice cloning using the Llasa TTS API. The voice should match the reference audio provided."
    tts_text = "This is an example of text-to-speech synthesis without reference audio using the Llasa TTS API."
    
    # Test text-to-speech (no reference audio needed)
    tts_success = text_to_speech_example(tts_text)
    
    # Test voice cloning (requires reference audio)
    reference_audio = None
    if len(sys.argv) > 1:
        reference_audio = sys.argv[1]
    else:
        # Look for common audio file names in current directory
        common_names = ["reference.wav", "reference.mp3", "test_audio.wav", "sample.wav"]
        for name in common_names:
            if os.path.exists(name):
                reference_audio = name
                break
    
    voice_clone_success = False
    if reference_audio:
        voice_clone_success = voice_clone_example(reference_audio, voice_clone_text)
    else:
        print("\n=== Voice Cloning Example ===")
        print("No reference audio file provided or found.")
        print("Usage: python example_api_usage.py [reference_audio.wav]")
        print("Or place a reference audio file named 'reference.wav' in the current directory.")
    
    # Summary
    print("\n" + "=" * 50)
    print("Summary:")
    print(f"  Text-to-speech: {'✓ Success' if tts_success else '✗ Failed'}")
    print(f"  Voice cloning: {'✓ Success' if voice_clone_success else '✗ Failed or skipped'}")
    
    if tts_success or voice_clone_success:
        print("\nGenerated audio files:")
        if tts_success and os.path.exists("text_to_speech_output.wav"):
            print("  - text_to_speech_output.wav")
        if voice_clone_success and os.path.exists("voice_clone_output.wav"):
            print("  - voice_clone_output.wav")
    
    return 0 if (tts_success or voice_clone_success) else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
