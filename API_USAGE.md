# Llasa TTS Voice Cloning API Usage Guide

This document provides comprehensive usage instructions for the Llasa TTS Voice Cloning API.

## Overview

The API provides two main endpoints:
- `/api/voice-clone` - Voice cloning with reference audio
- `/api/text-to-speech` - Text-only speech synthesis

## Starting the API Server

### API Server Only
```bash
python -m src.main --mode api --api-port 8000
```

### Gradio Interface Only (Default)
```bash
python -m src.main --mode gradio --gradio-port 7860
```

### Both API and Gradio
```bash
python -m src.main --mode both --api-port 8000 --gradio-port 7860
```

## API Endpoints

### 1. Health Check
**GET** `/health`

Check if the API server and models are ready.

```bash
curl http://localhost:8000/health
```

Response:
```json
{
  "status": "healthy",
  "models_initialized": true,
  "cuda_available": true
}
```

### 2. Voice Cloning
**POST** `/api/voice-clone`

Clone a voice using reference audio and synthesize new text with optimized backend settings.

#### Parameters:
- `reference_audio` (file): Reference audio file (automatically trimmed to 15 seconds)
- `text` (string): Text to synthesize

#### Automatic Backend Optimization:
- Model version: Fixed to 3B (best balance of quality and speed)
- Seed: Fixed to 0 (consistent results)
- Beam search: Disabled (faster generation)
- **Dynamic Length Optimization**: Automatically calculates optimal generation length based on text content
  - Analyzes word count and text complexity
  - Prevents audio cutting or unnecessary stretching
  - Ensures output matches input text exactly
- Temperature: 0.7 (optimal quality)
- Top-p: 0.9 (optimal diversity)
- Whisper language: Auto-detect

#### Example using curl:
```bash
curl -X POST "http://localhost:8000/api/voice-clone" \
  -F "reference_audio=@reference.wav" \
  -F "text=Hello, this is a test of voice cloning!" \
  --output synthesized_audio.wav
```

#### Example using Python:
```python
import requests

url = "http://localhost:8000/api/voice-clone"
data = {
    "text": "Hello, this is a test of voice cloning!"
}

with open("reference.wav", "rb") as audio_file:
    files = {"reference_audio": audio_file}
    response = requests.post(url, data=data, files=files)

if response.status_code == 200:
    with open("output.wav", "wb") as f:
        f.write(response.content)
    print("Voice cloning successful!")
else:
    print(f"Error: {response.status_code}")
```

### 3. Text-to-Speech
**POST** `/api/text-to-speech`

Generate speech from text without reference audio using optimized backend settings.

#### Parameters:
- `text` (string): Text to synthesize

#### Automatic Backend Optimization:
- Model version: Fixed to 3B (best balance of quality and speed)
- Seed: Fixed to 0 (consistent results)
- Beam search: Disabled (faster generation)
- **Dynamic Length Optimization**: Automatically calculates optimal generation length based on text content
  - Analyzes word count and text complexity
  - Prevents audio cutting or unnecessary stretching
  - Ensures output matches input text exactly
- Temperature: 0.7 (optimal quality)
- Top-p: 0.9 (optimal diversity)

#### Example using curl:
```bash
curl -X POST "http://localhost:8000/api/text-to-speech" \
  -F "text=Hello, this is a test of text to speech!" \
  --output synthesized_audio.wav
```

#### Example using Python:
```python
import requests

url = "http://localhost:8000/api/text-to-speech"
data = {
    "text": "Hello, this is a test of text to speech!"
}

response = requests.post(url, data=data)

if response.status_code == 200:
    with open("output.wav", "wb") as f:
        f.write(response.content)
    print("Text-to-speech successful!")
else:
    print(f"Error: {response.status_code}")
```

## Response Headers

Both endpoints return audio files with additional information in headers:
- `X-Sample-Rate`: Audio sample rate (typically 16000)
- `X-Seed-Used`: The random seed used for generation
- `X-Text-Length`: Length of the processed text

## Audio Processing

### Reference Audio (Voice Cloning)
- **Automatic Trimming**: Reference audio is automatically trimmed to exactly 15 seconds
- **Format Support**: Supports common audio formats (WAV, MP3, etc.)
- **Mono Conversion**: Stereo audio is automatically converted to mono
- **Resampling**: Audio is resampled to 16kHz for processing

### Output Audio
- **Format**: WAV (16-bit PCM)
- **Sample Rate**: 16kHz
- **Channels**: Mono

## Text Processing

### Dynamic Length Optimization
The API now features intelligent dynamic length optimization that ensures your audio output perfectly matches your input text:

#### How It Works:
1. **Text Analysis**: Analyzes word count and text complexity
2. **Token Estimation**: Calculates approximately 10-15 tokens per word for speech generation
3. **Smart Margins**: Adds appropriate margins based on generation mode:
   - Voice cloning: 30% margin for voice matching complexity
   - Text-only: 20% margin for efficient generation
4. **Adaptive Limits**: Sets both minimum and maximum generation lengths dynamically
5. **Natural Stopping**: Allows the model to stop naturally when the text is complete

#### Benefits:
- **No More Cutting**: Audio won't be cut off mid-sentence
- **No More Stretching**: Audio won't be artificially extended with silence or repetition
- **Perfect Matching**: Output duration matches the natural reading time of your text
- **Consistent Quality**: Works reliably across short phrases and long paragraphs

#### Text Limits:
- **Maximum Length**: Text longer than 1000 characters is automatically truncated
- **Encoding**: UTF-8 text encoding is supported
- **Word Count**: Optimized for 1-200 words (typical sentence to paragraph length)

## Error Handling

### Common Error Codes:
- `400`: Bad Request (invalid parameters, missing files, etc.)
- `500`: Internal Server Error (model errors, processing failures)

### Example Error Response:
```json
{
  "detail": "Reference file must be an audio file"
}
```

## Testing

Run the test suite to verify API functionality:

```bash
python test_api.py [optional_reference_audio.wav]
```

The test script will:
1. Check API health
2. Test text-to-speech endpoint
3. Test voice cloning endpoint (if reference audio provided)
4. Generate test audio files for verification

## Performance Optimization

### Efficient Model Loading ⚡
The API is optimized for production use with intelligent model management:

#### Startup Optimization:
- **Single Load**: All models (Llasa 3B, XCodec2, Whisper) are loaded once at API startup
- **No Reloading**: Models are cached and reused for all subsequent requests
- **Fast Startup**: Initial load takes ~30-60 seconds, then all requests are fast
- **Memory Efficient**: Models stay loaded in GPU memory without duplication

#### Request Performance:
- **All Requests Fast**: No model loading delays during inference (models pre-loaded)
- **Consistent Timing**: Predictable response times for all requests
- **Concurrent Support**: Efficiently handles multiple simultaneous requests
- **Memory Stable**: GPU memory usage remains constant across requests

### GPU Memory Requirements
- **CUDA Required**: Needs CUDA-capable GPU with sufficient VRAM
- **3B Model**: ~6-8GB VRAM (our fixed choice for optimal balance)
- **Memory Efficient**: Single model instances shared across all requests
- **No Memory Leaks**: Stable memory usage over time

### Generation Speed
- **Voice Cloning**: ~3-8 seconds (with dynamic length optimization)
- **Text-Only**: ~2-5 seconds (with dynamic length optimization)
- **Dynamic Length**: Generation time scales naturally with text length
- **No Cutting/Stretching**: Perfect audio length matching input text

## Integration Examples

### JavaScript/Node.js
```javascript
const FormData = require('form-data');
const fs = require('fs');
const axios = require('axios');

async function voiceClone(audioPath, text) {
    const form = new FormData();
    form.append('reference_audio', fs.createReadStream(audioPath));
    form.append('text', text);
    form.append('model_version', '3B');
    
    const response = await axios.post('http://localhost:8000/api/voice-clone', form, {
        headers: form.getHeaders(),
        responseType: 'arraybuffer'
    });
    
    fs.writeFileSync('output.wav', response.data);
}
```

### PHP
```php
$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => "http://localhost:8000/api/voice-clone",
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => [
        'reference_audio' => new CURLFile('reference.wav'),
        'text' => 'Hello from PHP!',
        'model_version' => '3B'
    ],
    CURLOPT_RETURNTRANSFER => true,
]);

$response = curl_exec($curl);
file_put_contents('output.wav', $response);
curl_close($curl);
```

## Troubleshooting

### Common Issues:
1. **CUDA not available**: Ensure you have a CUDA-capable GPU and drivers installed
2. **Model download failures**: Check internet connection and Hugging Face access
3. **Audio format errors**: Ensure reference audio is in a supported format
4. **Memory errors**: Try using a smaller model (1B instead of 8B)

### Debug Mode:
Check server logs for detailed error information when requests fail.
