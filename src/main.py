#!/usr/bin/env python3
import sys
import argparse
import torch

# Check Python version
if sys.version_info < (3, 10):
    print("ERROR: Python 3.10 or higher is required.")
    sys.exit(1)

if not torch.cuda.is_available():
    print("ERROR: CUDA is not available. Please use a CUDA-capable GPU.")
    sys.exit(1)

from .inference import initialize_models
from .models import get_llasa_model
from .app import build_dashboard

def main():
    parser = argparse.ArgumentParser(description="Run the modular Llasa TTS Dashboard.")
    parser.add_argument("--share", help="Enable gradio share", action="store_true")
    parser.add_argument("--mode", choices=["gradio", "api", "both"], default="gradio",
                       help="Run mode: 'gradio' for web UI only, 'api' for REST API only, 'both' for both")
    parser.add_argument("--api-port", type=int, default=8000, help="Port for API server")
    parser.add_argument("--gradio-port", type=int, default=7860, help="Port for Gradio interface")
    args = parser.parse_args()

    print("Initializing CUDA backend...", flush=True)
    torch.cuda.init()
    _ = torch.zeros(1).cuda()
    print(f"Using device: {torch.cuda.get_device_name()}", flush=True)

    # Initialize local models
    print("\nStep 1: Loading XCodec2 and Whisper models...", flush=True)
    initialize_models()

    print("\nStep 2: Preloading Llasa 3B model (faster startup for standard usage)...", flush=True)
    get_llasa_model("3B")
    print("Preload done. Models are ready!")

    if args.mode == "api":
        # Launch API server only
        print(f"\nLaunching API server on port {args.api_port}...", flush=True)
        import uvicorn
        from .api_server import app, initialize_api_models

        # Initialize API models (this will use the already loaded models)
        initialize_api_models()

        uvicorn.run(app, host="0.0.0.0", port=args.api_port)

    elif args.mode == "gradio":
        # Launch Gradio only
        print(f"\nLaunching Gradio interface on port {args.gradio_port}...", flush=True)
        app = build_dashboard()
        app.launch(share=args.share, server_name="0.0.0.0", server_port=args.gradio_port)

    elif args.mode == "both":
        # Launch both API and Gradio
        import threading
        import uvicorn
        from .api_server import app as api_app, initialize_api_models

        # Initialize API models (this will use the already loaded models)
        initialize_api_models()

        print(f"\nLaunching API server on port {args.api_port}...", flush=True)
        api_thread = threading.Thread(
            target=lambda: uvicorn.run(api_app, host="0.0.0.0", port=args.api_port),
            daemon=True
        )
        api_thread.start()

        print(f"Launching Gradio interface on port {args.gradio_port}...", flush=True)
        gradio_app = build_dashboard()
        gradio_app.launch(share=args.share, server_name="0.0.0.0", server_port=args.gradio_port)

if __name__ == "__main__":
    main()
