#!/usr/bin/env python3
"""
FastAPI server for Llasa TTS voice cloning API.
Provides REST API endpoints alongside the existing Gradio interface.
"""

import os
import io
import tempfile
import numpy as np
import torch
import torchaudio
import soundfile as sf
# Removed Optional import - no longer needed
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import Response
# Removed pydantic BaseModel - using simple form parameters

from .inference import infer_core, initialize_models
from .models import get_llasa_model


# Initialize FastAPI app
app = FastAPI(
    title="Llasa TTS Voice Cloning API",
    description="REST API for voice cloning using Llasa TTS models",
    version="1.0.0"
)

# Global flag to track if models are initialized
models_initialized = False


# Removed complex request/response models - API now uses simple form parameters


def initialize_api_models():
    """Initialize all models once at API startup."""
    global models_initialized
    if not models_initialized:
        print("🚀 Initializing models for API server...")
        print("This will take a moment but only happens once at startup...")

        # Initialize XCodec2 and Whisper models
        initialize_models()

        # Preload the 3B model (our fixed choice for API)
        print("📦 Loading Llasa 3B model...")
        get_llasa_model("3B")

        models_initialized = True
        print("✅ All models initialized successfully!")
        print("🎯 API is ready to serve requests!")
    else:
        print("ℹ️  Models already initialized, skipping...")


def process_audio_file(audio_file: UploadFile) -> str:
    """
    Process uploaded audio file and save to temporary location.
    Automatically trims to 15 seconds following Gradio logic.
    """
    # Save uploaded file to temporary location
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
        content = audio_file.file.read()
        temp_file.write(content)
        temp_path = temp_file.name
    
    # Load and process audio (trim to 15 seconds)
    try:
        waveform, sample_rate = torchaudio.load(temp_path)
        
        # Trim to 15 seconds if longer
        if (waveform.shape[1] / sample_rate) > 15:
            waveform = waveform[:, :sample_rate * 15]
        
        # Convert to mono if stereo
        if waveform.size(0) > 1:
            waveform = torch.mean(waveform, dim=0, keepdim=True)
        
        # Save processed audio back to temp file
        torchaudio.save(temp_path, waveform, sample_rate)
        
        return temp_path
    except Exception as e:
        # Clean up temp file on error
        if os.path.exists(temp_path):
            os.unlink(temp_path)
        raise HTTPException(status_code=400, detail=f"Error processing audio file: {str(e)}")


def validate_text_input(text: str) -> str:
    """Validate and process text input with dynamic length handling."""
    if not text or not text.strip():
        raise HTTPException(status_code=400, detail="Text input cannot be empty")
    
    text = text.strip()
    
    # Dynamic length handling - warn but allow longer text
    if len(text) > 1000:
        # For API, we'll truncate but inform the user
        text = text[:1000]
    
    return text


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Llasa TTS Voice Cloning API",
        "version": "1.0.0",
        "endpoints": {
            "/api/voice-clone": "POST - Voice cloning with reference audio",
            "/api/text-to-speech": "POST - Text-only speech synthesis",
            "/health": "GET - Health check"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    global models_initialized
    return {
        "status": "healthy",
        "models_initialized": models_initialized,
        "cuda_available": torch.cuda.is_available()
    }


@app.post("/api/voice-clone")
async def voice_clone(
    reference_audio: UploadFile = File(..., description="Reference audio file for voice cloning"),
    text: str = Form(..., description="Text to synthesize")
):
    """
    Voice cloning endpoint that accepts reference audio and text.
    Automatically trims reference audio to 15 seconds and processes using optimized backend settings.
    Fixed parameters: model_version=1B, seed=0, beam_search=False, auto_optimize=True
    Models are pre-loaded at startup for fast inference.
    """
    # Validate inputs
    if not reference_audio.content_type.startswith('audio/'):
        raise HTTPException(status_code=400, detail="Reference file must be an audio file")

    text = validate_text_input(text)

    temp_audio_path = None
    try:
        # Process reference audio file
        temp_audio_path = process_audio_file(reference_audio)

        # Run TTS inference with optimized backend settings
        result = infer_core(
            generation_mode="Reference audio",
            ref_audio_path=temp_audio_path,
            target_text=text,
            model_version="3B",           # Fixed to 3B
            hf_api_key=None,             # Use environment variable if needed
            trim_audio=True,             # Always trim to 15s for API
            max_length=2048,             # Initial value - will be dynamically adjusted
            temperature=0.7,             # Optimized default
            top_p=0.9,                   # Optimized default
            whisper_language="auto",     # Auto-detect language
            user_seed=0,                 # Fixed seed for consistency
            random_seed_each_gen=False,  # Use fixed seed
            beam_search_enabled=False,   # Disabled for speed
            auto_optimize_length=True,   # Always auto-optimize
            progress_callback=None
        )
        
        if result is None:
            raise HTTPException(status_code=500, detail="TTS generation failed")
        
        sample_rate, audio_np, seed_used = result
        
        # Convert audio to WAV format for response
        audio_int16 = (audio_np * 32767).astype(np.int16)
        with io.BytesIO() as wav_buffer:
            sf.write(wav_buffer, audio_int16, sample_rate, format='WAV', subtype='PCM_16')
            wav_data = wav_buffer.getvalue()
        
        # Return audio file as response
        return Response(
            content=wav_data,
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=synthesized_audio.wav",
                "X-Sample-Rate": str(sample_rate),
                "X-Seed-Used": str(seed_used),
                "X-Text-Length": str(len(text))
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    finally:
        # Clean up temporary audio file
        if temp_audio_path and os.path.exists(temp_audio_path):
            os.unlink(temp_audio_path)


@app.post("/api/text-to-speech")
async def text_to_speech(
    text: str = Form(..., description="Text to synthesize")
):
    """
    Text-only speech synthesis endpoint with optimized backend settings.
    Fixed parameters: model_version=3B, seed=0, beam_search=False, auto_optimize=True
    Models are pre-loaded at startup for fast inference.
    """
    # Validate inputs
    text = validate_text_input(text)

    try:
        # Run TTS inference in text-only mode with optimized settings
        result = infer_core(
            generation_mode="Text only",
            ref_audio_path=None,
            target_text=text,
            model_version="3B",           # Fixed to 3B
            hf_api_key=None,             # Use environment variable if needed
            trim_audio=False,            # Not applicable for text-only
            max_length=1200,             # Optimized default
            temperature=0.7,             # Optimized default
            top_p=0.9,                   # Optimized default
            whisper_language="auto",     # Not used in text-only mode
            user_seed=0,                 # Fixed seed for consistency
            random_seed_each_gen=False,  # Use fixed seed
            beam_search_enabled=False,   # Disabled for speed
            auto_optimize_length=True,   # Always auto-optimize
            progress_callback=None
        )
        
        if result is None:
            raise HTTPException(status_code=500, detail="TTS generation failed")
        
        sample_rate, audio_np, seed_used = result
        
        # Convert audio to WAV format for response
        audio_int16 = (audio_np * 32767).astype(np.int16)
        with io.BytesIO() as wav_buffer:
            sf.write(wav_buffer, audio_int16, sample_rate, format='WAV', subtype='PCM_16')
            wav_data = wav_buffer.getvalue()
        
        # Return audio file as response
        return Response(
            content=wav_data,
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=synthesized_audio.wav",
                "X-Sample-Rate": str(sample_rate),
                "X-Seed-Used": str(seed_used),
                "X-Text-Length": str(len(text))
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
